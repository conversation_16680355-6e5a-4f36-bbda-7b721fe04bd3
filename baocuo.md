Executing tasks: [:app:assembleDebug] in project C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1

Configuration on demand is an incubating feature.
> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors
> Task :app:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:generateDebugResources UP-TO-DATE
> Task :app:mergeDebugResources UP-TO-DATE
> Task :app:packageDebugResources UP-TO-DATE
> Task :app:parseDebugLocalResources UP-TO-DATE
> Task :app:dataBindingGenBaseClassesDebug UP-TO-DATE
> Task :app:generateSafeArgsDebug UP-TO-DATE
> Task :app:checkDebugAarMetadata UP-TO-DATE
> Task :app:mapDebugSourceSetPaths UP-TO-DATE
> Task :app:createDebugCompatibleScreenManifests UP-TO-DATE
> Task :app:extractDeepLinksDebug UP-TO-DATE
> Task :app:processDebugMainManifest UP-TO-DATE
> Task :app:processDebugManifest UP-TO-DATE
> Task :app:processDebugManifestForPackage UP-TO-DATE
> Task :app:processDebugResources UP-TO-DATE
> Task :app:javaPreCompileDebug UP-TO-DATE
> Task :app:mergeDebugShaders UP-TO-DATE
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets UP-TO-DATE
> Task :app:compressDebugAssets UP-TO-DATE
> Task :app:desugarDebugFileDependencies UP-TO-DATE
> Task :app:checkDebugDuplicateClasses UP-TO-DATE
> Task :app:mergeExtDexDebug UP-TO-DATE
> Task :app:mergeLibDexDebug UP-TO-DATE
> Task :app:mergeDebugJniLibFolders UP-TO-DATE
> Task :app:mergeDebugNativeLibs UP-TO-DATE
> Task :app:stripDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningDebug UP-TO-DATE
> Task :app:writeDebugAppMetadata UP-TO-DATE
> Task :app:writeDebugSigningConfigVersions UP-TO-DATE
> Task :app:kspDebugKotlin

> Task :app:compileDebugKotlin
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/db/AppDatabase.kt:69:34 The corresponding parameter in the supertype 'Migration' is named 'db'. This may cause problems when calling this function with named arguments.
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/CommentRepository.kt:176:9 Parameter 'commentId' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:114:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:127:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:141:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:152:13 Variable 'response' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:420:39 Unnecessary safe call on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:421:72 Elvis operator (?:) always returns the left operand of non-nullable type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:442:39 Unnecessary safe call on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt:443:72 Elvis operator (?:) always returns the left operand of non-nullable type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/repository/UserRepository.kt:540:57 Parameter 'subCount' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:456:32 Elvis operator (?:) always returns the left operand of non-nullable type Long
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt:648:47 Parameter 'limit' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/error/GlobalErrorHandler.kt:172:55 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/service/UnifiedPlaybackService.kt:383:37 Parameter 'error' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/comment/CommentFragment.kt:213:74 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:71:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:72:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:77:30 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:78:22 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:79:22 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:80:22 'SYSTEM_UI_FLAG_LAYOUT_STABLE: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:81:22 'SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:82:22 'SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:83:19 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:107:34 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:108:26 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:109:26 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:110:23 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:122:13 Variable 'progressBar' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:159:19 Enum argument can be null in Java, but exhaustive when contains no null branch
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:555:13 'overridePendingTransition(Int, Int): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:586:13 Variable 'etCaptcha' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/LyricView.kt:228:13 Variable 'visibleLineCount' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/LyricView.kt:250:49 Unnecessary non-null assertion (!!) on a non-null receiver of type String
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:235:13 Variable 'background' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/player/PlayerFragment.kt:284:87 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt:171:51 'overridePendingTransition(Int, Int): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:7:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:8:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:9:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:10:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:40:40 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:40:53 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:133:31 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:133:42 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:134:32 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:134:43 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:26 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:46 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:67 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:137:75 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:138:20 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:139:20 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:140:20 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:143:26 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:146:25 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:147:26 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:148:20 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtBlurCache.kt:165:22 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:193:17 Condition 'bitmap != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:232:21 Condition 'palette != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumArtCache.kt:393:35 Parameter 'colorInfo' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/AlbumRotationController.kt:445:13 Variable 'animatorSet' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:6:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:7:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:8:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:9:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:109:18 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:109:31 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:49 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:60 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:110:68 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:112:28 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:112:39 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:113:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:113:40 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:115:23 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:116:23 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:117:23 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:119:23 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/BlurUtils.kt:121:12 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt:162:61 'VIBRATOR_SERVICE: String' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt:167:23 'vibrate(Long): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/CacheManager.kt:269:9 This is a delicate API and its use requires care. Make sure you fully read and understand documentation of the declaration that is marked as a delicate API.
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/DiffCallbacks.kt:88:20 Condition 'oldItem.mediaId != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/DiffCallbacks.kt:88:47 Condition 'newItem.mediaId != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:16:29 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:17:29 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:18:29 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:19:29 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:118:13 Variable 'rect' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:163:18 'RenderScript' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:163:31 'create(Context!): RenderScript!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:26 'ScriptIntrinsicBlur' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:46 'create(RenderScript!, Element!): ScriptIntrinsicBlur!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:57 'Element' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:164:65 'U8_4(RenderScript!): Element!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:165:21 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:165:32 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:166:22 'Allocation' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:166:33 'createFromBitmap(RenderScript!, Bitmap!): Allocation!' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:167:20 'setRadius(Float): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:168:20 'setInput(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:169:20 'forEach(Allocation!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:170:16 'copyTo(Bitmap!): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:171:12 'destroy(): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:511:26 Unchecked cast: RequestBuilder<Drawable!> to RequestBuilder<Bitmap>
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:521:26 Unchecked cast: RequestBuilder<Drawable!> to RequestBuilder<Bitmap>
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/ImageUtils.kt:750:39 Parameter 'defaultColor' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:35:67 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:60:63 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/NetworkUtils.kt:85:67 'getter for isConnected: Boolean' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/PerformanceUtils.kt:65:41 'getWindowInsetsController(View): WindowInsetsControllerCompat?' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/utils/PlaylistCache.kt:73:50 'get(String!): Any?' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/CommentViewModel.kt:107:36 Parameter 'forceRefresh' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DiscoveryViewModel.kt:210:21 Variable 'result' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:214:35 Parameter 'voiceResult' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt:259:37 Parameter 'command' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:83:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:127:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/ExampleViewModel.kt:153:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:259:13 Variable 'lastError' is assigned but never accessed
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:269:17 The value 'e' assigned to 'var lastError: Throwable? defined in com.example.aimusicplayer.viewmodel.FlowViewModel.apiFlow.`<anonymous>`' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:316:13 Variable 'lastError' is assigned but never accessed
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/FlowViewModel.kt:349:17 The value 'e' assigned to 'var lastError: Throwable? defined in com.example.aimusicplayer.viewmodel.FlowViewModel.apiResponseFlow.`<anonymous>`' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:211:44 Variable 'username' initializer is redundant
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:284:36 Variable 'username' initializer is redundant
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/LoginViewModel.kt:338:59 Parameter 'avatarUrl' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:182:16 Condition 'username != null' is always 'true'
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/MainViewModel.kt:304:21 'when' is exhaustive so 'else' is redundant here
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/viewmodel/UserProfileViewModel.kt:154:17 'when' is exhaustive so 'else' is redundant here

> Task :app:compileDebugJavaWithJavac
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:8: 错误: 找不到符号
import com.example.aimusicplayer.model.LyricResponse;
                                      ^
  符号:   类 LyricResponse
  位置: 程序包 com.example.aimusicplayer.model
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:19: 错误: 找不到符号
import com.example.aimusicplayer.model.SongDetailResponse;
                                      ^
  符号:   类 SongDetailResponse
  位置: 程序包 com.example.aimusicplayer.model
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:22: 错误: 找不到符号
import com.example.aimusicplayer.model.UserDetailResponse;
                                      ^
  符号:   类 UserDetailResponse
  位置: 程序包 com.example.aimusicplayer.model
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:53: 错误: 找不到符号
    Call<SongDetailResponse> getSongDetail(@Query("ids") String ids);
         ^
  符号:   类 SongDetailResponse
  位置: 接口 UnifiedApiService
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:96: 错误: 找不到符号
    Call<LyricResponse> getLyric(@Query("id") String id);
         ^
  符号:   类 LyricResponse
  位置: 接口 UnifiedApiService
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\UnifiedApiService.java:226: 错误: 找不到符号
    Call<UserDetailResponse> getUserDetail(@Query("uid") String uid);
         ^
  符号:   类 UserDetailResponse
  位置: 接口 UnifiedApiService
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\RecommendSongsResponse.java:21: 错误: 程序包SongDetailResponse不存在
        private List<SongDetailResponse.Song> dailySongs;
                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\RecommendSongsResponse.java:24: 错误: 程序包SongDetailResponse不存在
        public List<SongDetailResponse.Song> getDailySongs() {
                                      ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\AlbumResponse.java:12: 错误: 程序包SongDetailResponse不存在
    private List<SongDetailResponse.Song> songs;
                                   ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\AlbumResponse.java:22: 错误: 程序包SongDetailResponse不存在
    public List<SongDetailResponse.Song> getSongs() {
                                  ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\AlbumResponse.java:33: 错误: 程序包SongDetailResponse不存在
        private List<SongDetailResponse.Artist> artists;
                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\model\AlbumResponse.java:48: 错误: 程序包SongDetailResponse不存在
        public List<SongDetailResponse.Artist> getArtists() {
                                      ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\discovery\TopListFragment.java:62: 错误: 找不到符号
        void onSongListLoaded(List<OnlineSong> songList, String playlistName);
                                   ^
  符号:   类 OnlineSong
  位置: 接口 OnSongListLoadedListener
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:6: 错误: 找不到符号
import com.example.aimusicplayer.model.LyricEntry;
                                      ^
  符号:   类 LyricEntry
  位置: 程序包 com.example.aimusicplayer.model
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:7: 错误: 找不到符号
import com.example.aimusicplayer.model.LyricInfo;
                                      ^
  符号:   类 LyricInfo
  位置: 程序包 com.example.aimusicplayer.model
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:35: 错误: 找不到符号
    public static LyricInfo parseLrc(String lrcContent) {
                  ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:121: 错误: 找不到符号
    public static LyricInfo parseWithTranslation(String originalLrc, String translatedLrc) {
                  ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:163: 错误: 找不到符号
    private static LyricEntry findClosestEntry(List<LyricEntry> entries, long time) {
                                                    ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:163: 错误: 找不到符号
    private static LyricEntry findClosestEntry(List<LyricEntry> entries, long time) {
                   ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:192: 错误: 找不到符号
    public static String toLrcString(LyricInfo lyricInfo) {
                                     ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\CookieInterceptor.java:43: 错误: 找不到符号
                        MusicApplication app = MusicApplication.getInstance();
                                                               ^
  符号:   方法 getInstance()
  位置: 类 MusicApplication
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:174: 错误: 对getPlaylist的引用不明确
        LiveData<List<MediaItem>> playlistLiveData = playerController.getPlaylist();
                                                                     ^
  PlayerController 中的方法 getPlaylist() 和 PlayerController 中的方法 getPlaylist() 都匹配
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\discovery\TopListFragment.java:149: 错误: 找不到符号
                        List<OnlineSong> songList = new ArrayList<>();
                             ^
  符号: 类 OnlineSong
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\discovery\TopListFragment.java:181: 错误: 找不到符号
                            OnlineSong song = new OnlineSong(
                            ^
  符号: 类 OnlineSong
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\discovery\TopListFragment.java:181: 错误: 找不到符号
                            OnlineSong song = new OnlineSong(
                                                  ^
  符号: 类 OnlineSong
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:285: 错误: 不兼容的类型: FrameLayout无法转换为LinearLayout
        android.widget.LinearLayout fragmentContainer = (android.widget.LinearLayout) binding.fragmentContainer;
                                                                                             ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:288: 错误: 不兼容的类型: LinearLayout无法转换为FrameLayout
        viewModel.initializeSidebarController((android.widget.LinearLayout) sidebarNav, btnMenuRight, fragmentContainer, navButtons);
                                                                                                      ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\JavaDiffCallbacks.java:40: 错误: 无法取消引用long
            return oldPlaylists.get(oldItemPosition).getId().equals(
                                                            ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:40: 错误: 找不到符号
        LyricInfo lyricInfo = new LyricInfo();
        ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:40: 错误: 找不到符号
        LyricInfo lyricInfo = new LyricInfo();
                                  ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:41: 错误: 找不到符号
        List<LyricEntry> entries = new ArrayList<>();
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:101: 错误: 找不到符号
                    entries.add(new LyricEntry(time, text, null));
                                    ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:107: 错误: 找不到符号
        Collections.sort(entries, Comparator.comparingLong(LyricEntry::getTime));
                                                           ^
  符号:   变量 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:123: 错误: 找不到符号
        LyricInfo originalInfo = parseLrc(originalLrc);
        ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:134: 错误: 找不到符号
        LyricInfo translatedInfo = parseLrc(translatedLrc);
        ^
  符号:   类 LyricInfo
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:140: 错误: 找不到符号
        List<LyricEntry> originalEntries = originalInfo.getEntries();
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:141: 错误: 找不到符号
        List<LyricEntry> translatedEntries = translatedInfo.getEntries();
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:144: 错误: 找不到符号
        for (LyricEntry originalEntry : originalEntries) {
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:148: 错误: 找不到符号
            LyricEntry closestEntry = findClosestEntry(translatedEntries, time);
            ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:168: 错误: 找不到符号
        LyricEntry closestEntry = null;
        ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:171: 错误: 找不到符号
        for (LyricEntry entry : entries) {
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:217: 错误: 找不到符号
        for (LyricEntry entry : lyricInfo.getEntries()) {
             ^
  符号:   类 LyricEntry
  位置: 类 LyricParser
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些消息已经过简化; 请使用 -Xdiags:verbose 重新编译以获得完整输出
42 个错误

> Task :app:compileDebugJavaWithJavac FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugJavaWithJavac'.
> Compilation failed; see the compiler error output for details.

* Try:
> Run with --info option to get more log output.
> Run with --scan to get full insights.

BUILD FAILED in 42s
34 actionable tasks: 4 executed, 30 up-to-date
