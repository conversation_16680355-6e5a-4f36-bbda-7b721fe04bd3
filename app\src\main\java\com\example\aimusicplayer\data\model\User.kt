package com.example.aimusicplayer.data.model

/**
 * 用户数据模型
 * @param userId 用户ID
 * @param username 用户名
 * @param token 用户令牌
 * @param avatarUrl 用户头像URL
 * @param isVip 是否VIP用户
 * @param level 用户等级
 * @param signature 个性签名
 * @param followeds 粉丝数
 * @param follows 关注数
 */
data class User(
    val userId: String,
    val username: String,
    var token: String,
    val avatarUrl: String,
    var isVip: Boolean = false,
    var level: Int = 0,
    var signature: String = "",
    var followeds: Int = 0,
    var follows: Int = 0
)
